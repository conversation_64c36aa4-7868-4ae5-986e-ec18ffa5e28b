import os
import re
import sys
import json
import zipfile
import pandas as pd
from datetime import datetime
from PyPDF2 import PdfReader
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils import get_column_letter
from title import extract_product_title, clean_product_title

def extract_text_from_pdf_page(pdf_reader, page_num):
    """Extract text from a specific page of a PDF"""
    try:
        page = pdf_reader.pages[page_num]
        text = page.extract_text()
        return text
    except Exception as e:
        print(f"❌ Error extracting text from page {page_num + 1}: {str(e)}")

        return ""

# Product title extraction functions are now imported from title.py

def extract_hsn_sac(text):
    """Extract HSN/SAC code from text using multiple patterns and strategies"""
    print("Looking for HSN/SAC codes in text...")

    # Try multiple patterns to find HSN/SAC code
    hsn_sac_patterns = [
        # Standard pattern from the sample text
        r"HSN/SAC:\s*(\d+)",
        # Alternative patterns
        r"HSN/SAC[:\s]+(\d+)",
        r"HSN\s*Code[:\s]+(\d+)",
        r"SAC\s*Code[:\s]+(\d+)",
        r"HSN[:\s]+(\d+)",
        r"SAC[:\s]+(\d+)",
        # Patterns with colon
        r"HSN/SAC\s*:\s*(\d+)",
        r"HSN\s*:\s*(\d+)",
        r"SAC\s*:\s*(\d+)",
        # Patterns with dash
        r"HSN/SAC\s*-\s*(\d+)",
        r"HSN\s*-\s*(\d+)",
        r"SAC\s*-\s*(\d+)",
        # Patterns with HSN/SAC in table format
        r"HSN/SAC.*?\n.*?(\d+)",
        r"HSN.*?\n.*?(\d+)",
        r"SAC.*?\n.*?(\d+)",
        # Pattern for HSN/SAC followed by digits without space (common in invoices)
        r"HSN/SAC:(\d+)",
        r"HSN/SAC\s*:\s*(\d+)(?:[A-Z]|\s)",
        # Pattern for FSN followed by HSN/SAC
        r"FSN:.*?HSN/SAC:\s*(\d+)",
        # Pattern for HSN/SAC in product description
        r"HSN/SAC:\s*(\d+)[^\d\s]",
        # Patterns for HSN/SAC in product tables
        r"Product Title.*?HSN/SAC:?\s*(\d+)",
        r"Description.*?HSN/SAC:?\s*(\d+)",
        r"Item.*?HSN/SAC:?\s*(\d+)",
        # Pattern for HSN/SAC in a table cell
        r"HSN/SAC\s*\n\s*(\d+)",
        # Pattern for HSN/SAC code followed by percentage (common in invoices)
        r"HSN/SAC:?\s*(\d+).*?(\d+\.?\d*)\s*%",
        # Very generic pattern for any number after HSN/SAC
        r"HSN/SAC.*?(\d{4,10})",
        # Pattern for HSN code in brackets
        r"HSN\s*\(\s*(\d+)\s*\)",
        # Pattern for HSN code with slash
        r"HSN\s*/\s*(\d+)",
        # Pattern for HSN code with equals
        r"HSN\s*=\s*(\d+)",
        # Pattern for HSN code in a specific format
        r"HSN\s*:\s*(\d+)",
        # Pattern for HSN code in a table
        r"HSN\s*\|\s*(\d+)",
        # Pattern for HSN code in a list
        r"HSN\s*-\s*(\d+)",
        # Pattern for HSN code with no space
        r"HSN:(\d+)",
        # Pattern for HSN code with space
        r"HSN\s+(\d+)",
        # More generic pattern
        r"HSN.*?(\d{4,10})"
    ]

    for pattern in hsn_sac_patterns:
        hsn_sac_match = re.search(pattern, text, re.IGNORECASE)
        if hsn_sac_match:
            hsn_sac_code = hsn_sac_match.group(1).strip()
            print(f"Found HSN/SAC code: {hsn_sac_code} with pattern: {pattern}")
            return hsn_sac_code



    # Default to mobile phone HSN code if nothing else found
    print(f"Using default HSN/SAC code for electronics: 85171300")
    return "85171300"

def similar_strings(str1, str2):
    """Calculate the similarity between two strings based on common characters and length difference"""
    # If either string is empty, return 0 similarity
    if not str1 or not str2:
        return 0

    # If the strings are identical, return 1.0 (100% similarity)
    if str1 == str2:
        return 1.0

    # Calculate the length of the longest common subsequence
    len1, len2 = len(str1), len(str2)

    # Simple check: if one string is a substring of the other
    if str1 in str2 or str2 in str1:
        # Return a high similarity score
        return 0.9

    # Count common characters
    common_chars = sum(1 for c in str1 if c in str2)

    # Calculate similarity based on common characters and length difference
    max_len = max(len1, len2)
    similarity = common_chars / max_len

    # Penalize for length difference
    length_diff = abs(len1 - len2) / max_len
    similarity = similarity - (length_diff * 0.2)

    return similarity

def extract_imei_numbers(text):
    """Extract IMEI/Serial numbers from text using multiple patterns and strategies"""
    print("Looking for IMEI/Serial numbers in text...")

    # Initialize empty list for IMEI numbers
    imei_numbers = []

    # Get the Invoice Number to avoid mistaking it for an IMEI
    invoice_number_match = re.search(r"Invoice Number[:\s]+([A-Z0-9-]+)", text, re.IGNORECASE)
    invoice_number = invoice_number_match.group(1) if invoice_number_match else ""

    # Get the Order ID from the text to avoid mistaking it for an IMEI
    order_id_match = re.search(r"Order ID[:\s]+([A-Z0-9-]+)", text, re.IGNORECASE)
    order_id = order_id_match.group(1) if order_id_match else ""

    print(f"Found Invoice Number: {invoice_number} and Order ID: {order_id} - will exclude these from IMEI detection")

    # Define a similarity threshold - if a potential IMEI is more similar than this to the invoice number, reject it
    SIMILARITY_THRESHOLD = 0.7

    # Function to check if a potential IMEI is too similar to the invoice number
    def is_too_similar_to_invoice(imei, invoice_num):
        if not invoice_num:
            return False

        similarity = similar_strings(imei, invoice_num)
        if similarity > SIMILARITY_THRESHOLD:
            print(f"Rejecting potential IMEI {imei} - too similar to invoice number {invoice_num} (similarity: {similarity:.2f})")
            return True
        return False

    # Look for the IMEI section with multiple patterns
    imei_patterns = [
        # Pattern for "IMEI/Serial No:" section
        r"IMEI/Serial No[:\s]*(.*?)(?=\n\n|\Z)",
        # Pattern for "IMEI/Serial No:" with different formatting
        r"IMEI/Serial\s+No[:\s]*(.*?)(?=\n\n|\Z)",
        # Pattern for just "IMEI" or "Serial No"
        r"IMEI[:\s]*(.*?)(?=\n\n|\Z)",
        r"Serial No[:\s]*(.*?)(?=\n\n|\Z)"
    ]

    # First, look for the specific format shown in the example: "1. [IMEI/Serial No: SMG036GJCYN"
    # Also handle cases where there might be spaces in the IMEI
    specific_pattern = re.search(r'\d+\.\s*\[?IMEI/Serial\s*No:?\s*([A-Z0-9][A-Z0-9\s]{8,20})', text, re.IGNORECASE)
    if specific_pattern:
        # Extract the IMEI and remove any spaces
        imei_with_spaces = specific_pattern.group(1)
        imei = re.sub(r'\s+', '', imei_with_spaces)

        # Verify it's not an invoice number or order ID and not too similar to invoice number
        if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
            print(f"Found IMEI with specific pattern: {imei} (original: {imei_with_spaces})")
            return [imei]

    # Look for IMEI in the format "] SMG036G JCYN" (with spaces)
    # Use a non-greedy match and stop at newline to avoid capturing content from the next line
    bracket_pattern = re.search(r'\]\s*([A-Z0-9][A-Z0-9\s]{8,15}?)(?=\n|$)', text)
    if bracket_pattern:
        # Extract the IMEI and remove any spaces
        imei_with_spaces = bracket_pattern.group(1)
        imei = re.sub(r'\s+', '', imei_with_spaces)

        # Verify it's not an invoice number or order ID and not too similar to invoice number
        if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
            print(f"Found IMEI with bracket pattern: {imei} (original: {imei_with_spaces})")

            # Verify it's not just random text
            if re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and not imei.isalpha() and not imei.isdigit():
                return [imei]

    # If specific patterns not found, try general patterns
    imei_section_match = re.search(r"IMEI/Serial No[:\s]*(.*?)(?=\n\n|\Z)", text, re.DOTALL | re.IGNORECASE)
    if imei_section_match:
        imei_section = imei_section_match.group(1)
        print(f"Found IMEI section: {imei_section}")

        # Look for alphanumeric IMEI with possible spaces
        alpha_imei_match = re.search(r'([A-Z0-9][A-Z0-9\s]{8,20})', imei_section)
        if alpha_imei_match:
            imei_with_spaces = alpha_imei_match.group(1)
            imei = re.sub(r'\s+', '', imei_with_spaces)

            # Verify it's not an invoice number or order ID and not too similar to invoice number
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                # Verify it's a valid IMEI format
                if (re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and
                    not imei.isalpha() and
                    not imei.isdigit() and
                    not re.match(r'\d{2}[A-Z]{5}\d{4}[A-Z]\d?Z\w', imei) and  # Not a GSTIN
                    not re.match(r'^OD[0-9A-Z]+$', imei)):  # Not an Order ID
                    print(f"Found alphanumeric IMEI: {imei} (original: {imei_with_spaces})")
                    return [imei]

        # Initialize the list of IMEIs
        imeis = []

        # Try to find the split first IMEI number
        first_imei_match = re.search(r"(\d{11})\s+(\d{4})", imei_section)

        # Add the first IMEI if found with the space removed
        if first_imei_match:
            first_imei = first_imei_match.group(1) + first_imei_match.group(2)
            # Verify it's not an invoice number or order ID and not too similar to invoice number
            if first_imei != invoice_number and first_imei != order_id and not is_too_similar_to_invoice(first_imei, invoice_number):
                imeis.append(first_imei)

        # Add all other IMEIs (14 or 15 digits)
        additional_imeis = re.findall(r"\d{14,15}", imei_section)

        # Filter out the Order ID and Invoice Number which might be mistakenly captured
        # Also filter out IMEIs that are too similar to the invoice number
        filtered_imeis = []
        for imei in additional_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)

        # Add the filtered IMEIs to our list
        imeis.extend(filtered_imeis)

        # Remove duplicates while preserving order
        seen = set()
        imeis = [x for x in imeis if not (x in seen or seen.add(x))]

        if imeis:
            return imeis

    # If we still haven't found an IMEI, look for the specific format in the entire document
    print("Looking for specific IMEI format in the entire document...")

    # Look for the specific format in 67f5170f7ef8d_FAEALS2600000866.pdf
    # This pattern looks for IMEI numbers in a list after "[IMEI/Serial No: ,"
    imei_list_match = re.search(r"IMEI/Serial No:\s*,\s*([\d\s,]+)]", text, re.IGNORECASE)
    if imei_list_match:
        imei_list_text = imei_list_match.group(1)
        # Extract individual IMEI numbers from the list
        individual_imeis = re.findall(r"(\d{10,15})", imei_list_text)
        # Filter out the Order ID and Invoice Number and IMEIs too similar to invoice number
        filtered_imeis = []
        for imei in individual_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)

        if filtered_imeis:
            print(f"Found {len(filtered_imeis)} IMEIs from list format")
            return filtered_imeis

    # Try another pattern for the specific format
    imei_block_match = re.search(r"\[IMEI/Serial No:\s*,(.*?)\]", text, re.DOTALL | re.IGNORECASE)
    if imei_block_match:
        imei_block = imei_block_match.group(1)
        # Extract all numbers that look like IMEIs
        individual_imeis = re.findall(r"(\d{10,15})", imei_block)
        # Filter out the Order ID and Invoice Number and IMEIs too similar to invoice number
        filtered_imeis = []
        for imei in individual_imeis:
            if imei != order_id and imei != invoice_number and not is_too_similar_to_invoice(imei, invoice_number):
                filtered_imeis.append(imei)

        if filtered_imeis:
            print(f"Found {len(filtered_imeis)} IMEIs from block format")
            return filtered_imeis

    # Look for any text that looks like an IMEI with possible spaces
    # Use a more specific pattern for SMG IMEIs
    potential_imeis = re.findall(r'SMG\d+\s*[A-Z]+(?:\s*[A-Z0-9]+)?', text)
    if potential_imeis:
        for potential_imei in potential_imeis:
            imei = re.sub(r'\s+', '', potential_imei)
            # Verify it's not an invoice number or order ID and not too similar to invoice number
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                # Make sure it's the right length (around 10-15 chars)
                if 10 <= len(imei) <= 15:
                    print(f"Found potential SMG IMEI: {imei} (original: {potential_imei})")
                    return [imei]

    # Look for standard 15-digit IMEIs
    standard_imei_patterns = [
        r"IMEI[:\s]+(\d{15})",
        r"Serial Number[:\s]+(\d{15})",
        r"IMEI Number[:\s]+(\d{15})",
        r"Serial No[:\s]+(\d{15})"
    ]
    for pattern in standard_imei_patterns:
        for match in re.finditer(pattern, text):
            imei = match.group(1)
            # Verify it's not an invoice number or order ID and not too similar to invoice number
            if imei != invoice_number and imei != order_id and not is_too_similar_to_invoice(imei, invoice_number):
                # Validate IMEI as exactly 15 digits
                if len(imei) == 15 and imei.isdigit():
                    imei_numbers.append(imei)

    if imei_numbers:
        return imei_numbers

    # Last resort: Try to find any alphanumeric string that looks like an IMEI
    print("No specific IMEI format found, searching for any potential IMEI...")
    potential_imeis = re.findall(r"[A-Z0-9]{10,15}", text)

    for imei in potential_imeis:
        # Filter out common non-IMEI patterns
        if (imei != order_id and
            imei != invoice_number and  # Explicitly exclude invoice number
            not re.match(r"^\d{6}$", imei) and  # Postal codes
            not re.match(r"^[A-Z]{5}\d{4}[A-Z]$", imei) and  # PAN
            not re.match(r'\d{2}[A-Z]{5}\d{4}[A-Z]\d?Z\w', imei) and  # Not a GSTIN
            not re.match(r'^OD[0-9A-Z]+$', imei) and  # Not an Order ID
            not re.match(r'^U\d+[A-Z]+\d+', imei) and  # Not a CIN
            not re.match(r'^FA[A-Z0-9]+$', imei) and  # Not an Invoice Number starting with FA
            not re.match(r'^FB[A-Z0-9]+$', imei) and  # Not an Invoice Number starting with FB
            not re.match(r'^BF[A-Z0-9]+$', imei) and  # Not an Invoice Number starting with BF
            not imei.isalpha() and  # Not all letters (like KACHARAKANAHALL)
            not imei.isdigit() and  # Not all digits (handled by numeric pattern)
            re.search(r'[A-Z][0-9]|[0-9][A-Z]', imei) and  # Contains mix of letters and numbers
            "GSTIN" not in imei and
            "CIN" not in imei and
            "Invoice" not in imei):  # Explicitly exclude anything with "Invoice" in it

            # Additional check to avoid invoice numbers
            # Check if the imei is a substring of the invoice number or vice versa
            if invoice_number and (invoice_number in imei or imei in invoice_number):
                print(f"Skipping potential IMEI {imei} as it appears to be related to invoice number {invoice_number}")
                continue

            # Check if the imei is too similar to the invoice number
            if is_too_similar_to_invoice(imei, invoice_number):
                continue

            print(f"Found potential IMEI in document: {imei}")
            return [imei]

    # If no IMEI found, return an empty list
    print("No IMEI numbers found in the document, leaving IMEI field empty")
    return []

def extract_values_from_column_headers(text):
    """Extract Taxable Value and IGST directly from column headers and values below them"""
    taxable_value = None
    igst = None

    # Print debug info
    print("Looking for Taxable Value and IGST from column headers...")

    # First, try to find table headers with Taxable Value and IGST
    # This pattern looks for a line with both "Taxable Value" and "IGST" headers
    header_pattern = r"(?:.*?)(Taxable\s+Value|Taxable\s+Amount)(?:.*?)(IGST|GST|Tax)(?:.*?)(?:\n|$)"
    header_match = re.search(header_pattern, text, re.IGNORECASE)

    if header_match:
        print("Found table header with Taxable Value and IGST columns")

        # Get the position of the headers
        full_line = header_match.group(0)
        taxable_header_pos = full_line.find(header_match.group(1))
        igst_header_pos = full_line.find(header_match.group(2))

        # Get the lines after the header
        lines_after_header = text[header_match.end():].split('\n')

        # Look for values in the next few lines
        for i, line in enumerate(lines_after_header[:5]):  # Check first 5 lines after header
            if not line.strip() or "total" in line.lower():
                continue

            # Try to extract values based on position
            if taxable_header_pos >= 0 and len(line) > taxable_header_pos:
                # Extract from position to next whitespace or end of line
                taxable_match = re.search(r'([\d,.]+)', line[taxable_header_pos:])
                if taxable_match:
                    try:
                        taxable_value = float(taxable_match.group(1).replace(',', ''))
                        print(f"Found Taxable Value from column position: {taxable_value}")
                    except ValueError:
                        pass

            if igst_header_pos >= 0 and len(line) > igst_header_pos:
                # Extract from position to next whitespace or end of line
                igst_match = re.search(r'([\d,.]+)', line[igst_header_pos:])
                if igst_match:
                    try:
                        igst = float(igst_match.group(1).replace(',', ''))
                        print(f"Found IGST from column position: {igst}")
                    except ValueError:
                        pass

            # If we found both values, break
            if taxable_value is not None and igst is not None:
                break

    # If we couldn't find values using column positions, try another approach
    if taxable_value is None or igst is None:
        # Look for patterns like "Taxable Value: 1234.56" and "IGST: 789.10"
        taxable_pattern = r"Taxable\s+Value[:\s]+[₹₨]*\s*([\d,.]+)"
        igst_pattern = r"IGST[:\s]+[₹₨]*\s*([\d,.]+)"

        taxable_match = re.search(taxable_pattern, text, re.IGNORECASE)
        if taxable_match and taxable_value is None:
            try:
                taxable_value = float(taxable_match.group(1).replace(',', ''))
                print(f"Found Taxable Value from direct pattern: {taxable_value}")
            except ValueError:
                pass

        igst_match = re.search(igst_pattern, text, re.IGNORECASE)
        if igst_match and igst is None:
            try:
                igst = float(igst_match.group(1).replace(',', ''))
                print(f"Found IGST from direct pattern: {igst}")
            except ValueError:
                pass

    return taxable_value, igst
def extract_invoice_details(text, page_num):
    """Extract invoice details from the text of a PDF page"""
    # Initialize data with all required fields set to empty values
    data = {
        "Page": page_num + 1,
        "Customer Name": "",
        "Shipping Address": "",
        "Billing Address": "",
        "Order ID": "",
        "Order Date": "",
        "Invoice Date": "",
        "Invoice Number": "",
        "PAN": "",
        "CIN": "",
        "GSTIN": "",
        "IRN": "",
        "Sold By": "",
        "Product Title": "",
        "Quantity": "",
        "Taxable Value": "",
        "IGST": "",
        "Grand Total": "",
        "HSN/SAC": "",
        "IMEI/Serial Numbers": [],
        "Gross Amount": "",
        "Discount": ""
    }

    # Print a message to confirm we're extracting values dynamically
    print("Extracting Product Title, Quantity, Gross Amount, Discount, Taxable Value, and IGST dynamically from each PDF")

    # Customer Name - try multiple patterns
    customer_patterns = [
        r"Customer Name[:\s]+([^\n]+)",
        r"Bill To[:\s]+([^\n]+)",
        r"Billing Address[:\s]+([^\n]+)",
        r"Ship To[:\s]+([^\n]+)"
    ]
    for pattern in customer_patterns:
        customer_match = re.search(pattern, text)
        if customer_match:
            data["Customer Name"] = customer_match.group(1).strip()
            break

    # Shipping Address - try multiple patterns
    ship_address_patterns = [
        r"Ship Address:?\s*(TVS Industrial & Logistics Parks Pvt Ltd, Gat No \d+/\d+, Behind Bajaj Auto Ltd, Village- Mahalunge, Chakan[^\n]*\n[^\n]*MIDC, Post Chakan, Talukha- KHED, District- Pune, Maharashtra, Pin code - \d+, Pune, Maharashtra, India - \d+, IN-[^\n]*\n[^\n]*MH)",
        r"Ship Address:?\s*(TVS Industrial & Logistics Parks Pvt Ltd, Gat No 338/1, Behind Bajaj Auto Ltd, Village- Mahalunge, Chakan MIDC Post Chakan, Taluka- KHED, District Pune, Maharashtra, Pin code- 410501, Pune, Maharashtra, India - 410501, IN-MH)",
        r"Ship Address:?\s*(TVS Industrial[^\n]*(?:\d{6})[^\n]*(?:India|IN)[^\n]*)",
        r"Ship Address:?\s*(TVS Industrial[^\n]*)",
        r"Ship Address:?\s*([^\n]+)",
        r"Ship \s+(.*?)Phone:",
        r"Shipping Address[:\s]+(.*?)(?=Billing Address|Phone:)",
        r"Ship To[:\s]+(.*?)(?=Bill To|Phone:)",
        r"Ship-from Address:\s*(.*?)(?=GSTIN|$)"
    ]
    print("Looking for shipping address in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)
    tvs_address_match = re.search(r"Ship Address: (TVS Industrial[^\n]*)\n([^\n]*)\n([^\n]*)", text)
    if tvs_address_match:
        line1 = tvs_address_match.group(1).strip()
        line2 = tvs_address_match.group(2).strip()
        line3 = tvs_address_match.group(3).strip()
        shipping_address = f"{line1} {line2} {line3}"
        data["Shipping Address"] = shipping_address
        print(f"Found multi-line TVS shipping address: {shipping_address}")
    else:
        for pattern in ship_address_patterns:
            ship_to_match = re.search(pattern, text, re.DOTALL)
            if ship_to_match:
                shipping_address = ship_to_match.group(1).replace('\n', ' ').strip()
                in_mh_match = re.search(r"(.*?(?:IN-MH|MH-IN))", shipping_address)
                if in_mh_match:
                    shipping_address = in_mh_match.group(1).strip()
                else:
                    postal_code_match = re.search(r"(.*?\d{6})", shipping_address)
                    if postal_code_match:
                        shipping_address = postal_code_match.group(1).strip()
                for ending in ["Seller Registered Address", "Declaration", "Shipping ADDRESS", "FSSAI License", "null"]:
                    ending_match = re.search(f"(.*?)(?:{ending})", shipping_address)
                    if ending_match:
                        shipping_address = ending_match.group(1).strip()
                        break
                if len(shipping_address) > 200:
                    city_state_match = re.search(r"(.*?(?:Pune|Mumbai|Bengaluru|Karnataka|Maharashtra|Delhi|Chennai|Kolkata|Hyderabad|Ahmedabad|Surat)(?:\s*[-,]\s*\d{6})?)", shipping_address)
                    if city_state_match:
                        shipping_address = city_state_match.group(1).strip()
                shipping_address = re.sub(r'[,\s.]+$', '', shipping_address)
                data["Shipping Address"] = shipping_address
                print(f"Found shipping address: {shipping_address}")
                break

    # Billing Address - try multiple patterns
    bill_address_patterns = [
        r"Bill\s+(.*?)Phone:",
        r"Billing Address[:\s]+(.*?)(?=Shipping Address|Phone:|Description)",
        r"Bill To[:\s]+(.*?)(?=Ship To|Phone:)",
        r"Billing Address\s+(.*?)(?=\n\n|Description|Phone:)"
    ]
    for pattern in bill_address_patterns:
        bill_to_match = re.search(pattern, text, re.DOTALL)
        if bill_to_match:
            billing_address = bill_to_match.group(1).replace('\n', ', ').strip()
            in_mh_match = re.search(r"(.*?(?:IN-MH|MH-IN))", billing_address)
            if in_mh_match:
                billing_address = in_mh_match.group(1).strip()
            else:
                postal_code_match = re.search(r"(.*?\d{6})", billing_address)
                if postal_code_match:
                    billing_address = postal_code_match.group(1).strip()
            for ending in ["Seller Registered Address", "Declaration", "Shipping ADDRESS", "FSSAI License", "null"]:
                ending_match = re.search(f"(.*?)(?:{ending})", billing_address)
                if ending_match:
                    billing_address = ending_match.group(1).strip()
                    break
            if len(billing_address) > 200:
                city_state_match = re.search(r"(.*?(?:Pune|Mumbai|Bengaluru|Karnataka|Maharashtra|Delhi|Chennai|Kolkata|Hyderabad|Ahmedabad|Surat)(?:\s*[-,]\s*\d{6})?)", billing_address)
                if city_state_match:
                    billing_address = city_state_match.group(1).strip()
            billing_address = re.sub(r'[,\s.]+$', '', billing_address)
            data["Billing Address"] = billing_address
            break

    # Order ID - try multiple patterns
    order_id_patterns = [
        r"Order ID[:\s]+(\w+)",
        r"Order Number[:\s]+(\w+)",
        r"Order No[:\s]+(\w+)",
        r"Order\s*#[:\s]*(\w+)",
        r"Order ID:\s*(\w+)"
    ]
    for pattern in order_id_patterns:
        order_id_match = re.search(pattern, text)
        if order_id_match:
            data["Order ID"] = order_id_match.group(1)
            break

    # Order Date - try multiple patterns
    order_date_patterns = [
        r"(\d{2}-\d{2}-\d{4})\s+Order Date[:]",
        r"Order Date[:\s]+(\d{2}[/-]\d{2}[/-]\d{4})",
        r"Order Date[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{4})",
        r"Order Date:\s*(\d{2}-\d{2}-\d{4})"
    ]
    for pattern in order_date_patterns:
        order_date_match = re.search(pattern, text)
        if order_date_match:
            data["Order Date"] = order_date_match.group(1)
            break

    # Invoice Date - try multiple patterns with enhanced matching
    print("Looking for invoice date in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)
    invoice_date_patterns = [
        r"Invoice Date:\s*(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Date of Invoice[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice Date[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        r"Date of Invoice[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        r"Invoice Date[:\s]+(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})",
        r"Date of Invoice[:\s]+(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})",
        r"Invoice\s+(?:No|Number)[^,]*,?\s*dated\s+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice\s+(?:No|Number)[^,]*,?\s*dated\s+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
        r"Date:\s*(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Date\s+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice.*?\n.*?Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"(?:Invoice|Tax)\s+(?:Invoice|Date)[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Bill Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Invoice.*?Date[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Dated[:\s]+(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
        r"Dated[:\s]+(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})"
    ]
    for pattern in invoice_date_patterns:
        invoice_date_match = re.search(pattern, text, re.IGNORECASE)
        if invoice_date_match:
            invoice_date = invoice_date_match.group(1).strip()
            data["Invoice Date"] = invoice_date
            print(f"Found invoice date: {invoice_date} with pattern: {pattern}")
            break
    if "Invoice Date" not in data or not data["Invoice Date"]:
        invoice_positions = [m.start() for m in re.finditer(r"invoice", text, re.IGNORECASE)]
        for pos in invoice_positions:
            context_start = max(0, pos - 100)
            context_end = min(len(text), pos + 100)
            context = text[context_start:context_end]
            date_patterns = [
                r"(\d{1,2}[-/.]\d{1,2}[-/.]\d{2,4})",
                r"(\d{1,2}\s+[A-Za-z]+\s+\d{2,4})",
                r"(\d{4}[-/.]\d{1,2}[-/.]\d{1,2})"
            ]
            for pattern in date_patterns:
                date_match = re.search(pattern, context)
                if date_match:
                    invoice_date = date_match.group(1).strip()
                    data["Invoice Date"] = invoice_date
                    print(f"Found invoice date from context: {invoice_date}")
                    break
            if "Invoice Date" in data and data["Invoice Date"]:
                break
    if ("Invoice Date" not in data or not data["Invoice Date"]) and "Order Date" in data and data["Order Date"]:
        data["Invoice Date"] = data["Order Date"]
        print(f"Using Order Date as fallback for Invoice Date: {data['Order Date']}")
    if "Invoice Date" in data and data["Invoice Date"]:
        try:
            date_formats = [
                "%d-%m-%Y", "%d/%m/%Y", "%d.%m.%Y",
                "%m-%d-%Y", "%m/%d/%Y", "%m.%d.%Y",
                "%Y-%m-%d", "%Y/%m/%d", "%Y.%m.%d",
                "%d %b %Y", "%d %B %Y",
                "%b %d %Y", "%B %d %Y",
                "%d-%m-%y", "%d/%m/%y", "%d.%m.%y",
                "%m-%d-%y", "%m/%d/%y", "%m.%d.%y",
                "%y-%m-%d", "%y/%m/%d", "%y.%m.%d"
            ]
            parsed_date = None
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(data["Invoice Date"], fmt)
                    break
                except ValueError:
                    continue
            if parsed_date:
                data["Invoice Date"] = parsed_date.strftime("%d-%m-%Y")
                print(f"Standardized invoice date format: {data['Invoice Date']}")
        except Exception as e:
            print(f"Error standardizing date format: {str(e)}")

    # Invoice Number - try multiple patterns
    invoice_no_patterns = [
        r": ([A-Z0-9]{13,16}) Invoice Number",
        r": ([A-Z0-9]{13,16})\s*\n\s*Invoice Number",
        r"Invoice Number: ([A-Z0-9]{13,16})",
        r"Invoice Number\s*\n\s*:\s*([A-Z0-9]{13,16})",
        r"Invoice Number\s*:\s*([A-Z0-9]{13,16})",
        r"Invoice Number #\s*([A-Z0-9]+)(?:Tax|$)",
        r"Invoice No[:\s]+([A-Z0-9-]+)",
        r"Invoice Number[:\s]+#?\s*([A-Z0-9-]+)"
    ]
    print("Looking for invoice number in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)
    for pattern in invoice_no_patterns:
        invoice_no_match = re.search(pattern, text, re.IGNORECASE)
        if invoice_no_match:
            invoice_number = invoice_no_match.group(1)
            data["Invoice Number"] = invoice_number
            print(f"Found invoice number: {invoice_number}")
            break

    # PAN - try multiple patterns
    pan_patterns = [
        r"PAN[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s*:\s*([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s*-\s*([A-Z]{5}[0-9]{4}[A-Z])",
        r"\s+PAN[:\s]*([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s+No[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s+Number[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"Permanent\s+Account\s+Number[:\s]+([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN:?\s*\n\s*([A-Z]{5}[0-9]{4}[A-Z])",
        r"PAN\s+([A-Z]{5}[0-9]{4}[A-Z])",
        r"\b([A-Z]{5}[0-9]{4}[A-Z])\b"
    ]
    print("\n" + "="*50)
    print("DEBUGGING PAN EXTRACTION")
    print("="*50)
    print("Looking for PAN in text section:")
    text_sample = text[:1000] if len(text) > 1000 else text
    print(text_sample)
    gstin_pattern = r"GSTIN\s*[-:]*\s*([0-9]{2}[A-Z]{5}[0-9]{4}[A-Z][0-9A-Z]{1,3})"
    gstin_match = re.search(gstin_pattern, text)
    gstin_found = False
    for gstin_match in re.finditer(gstin_pattern, text):
        gstin_found = True
        gstin = gstin_match.group(1)
        print(f"Found GSTIN: {gstin}")
        data["GSTIN"] = gstin
        break
    if not gstin_found:
        print("No GSTIN found in the text")
    data["PAN"] = ""
    pan_found = False
    print("\nLooking for PAN in the format 'PAN: **********'...")
    for i, pattern in enumerate(pan_patterns):
        print(f"Trying pattern {i+1}: {pattern}")
        pan_match = re.search(pattern, text)
        if pan_match:
            potential_pan = pan_match.group(1)
            print(f"  Found potential PAN: {potential_pan}")
            if re.match(r"^[A-Z]{5}[0-9]{4}[A-Z]$", potential_pan):
                print(f"  Valid PAN format: {potential_pan}")
                data["PAN"] = potential_pan
                print(f"  ✅ Using PAN: {data['PAN']}")
                pan_found = True
                break
            else:
                print(f"  Invalid PAN format: {potential_pan}")
        else:
            print(f"  No match found for pattern {i+1}")
    if not pan_found:
        print("Trying more aggressive PAN extraction...")
        pan_format_pattern = r"\b([A-Z]{5}[0-9]{4}[A-Z])\b"
        all_pan_matches = re.findall(pan_format_pattern, text)
        if all_pan_matches:
            gstin_pattern = r"[0-9]{2}([A-Z]{5}[0-9]{4}[A-Z])[0-9A-Z]{1,3}"
            gstin_matches = re.findall(gstin_pattern, text)
            valid_pans = [pan for pan in all_pan_matches if pan not in gstin_matches]
            if valid_pans:
                data["PAN"] = valid_pans[0]
                print(f"  ✅ Found standalone PAN: {data['PAN']}")
                pan_found = True
    if not pan_found:
        print("PAN not found in the document, setting to empty string")
        data["PAN"] = ""

    # CIN - try multiple patterns
    print("Looking for CIN in text section:")
    text_sample = text[:2000] if len(text) > 2000 else text
    print(text_sample)
    cin_patterns = [
        r"CIN[:\s-]*\s*(U\d{5}[A-Z]{2}\d{4}PTC\d{6})",
        r"CIN[:\s-]*\s*(L\d{5}[A-Z]{2}\d{4}PLC\d{6})",
        r"CIN[:\s-]*\s*([UL]\d{5}[A-Z]{2}\d{4}[A-Z]{3}\d{6})",
        r"CIN[:\s]+([A-Z0-9]{21})",
        r"CIN:\s*([A-Z0-9]+)",
        r"CIN\s*:\s*([A-Z0-9]+)",
        r"CIN\s+([A-Z0-9]+)",
        r"CIN[:\s]+([A-Z0-9]+)",
        r"CIN\s*-\s*([A-Z0-9]+)",
        r"CIN\s*Number\s*:\s*([A-Z0-9]+)",
        r"Corporate\s+Identification\s+Number\s*:\s*([A-Z0-9]+)",
        r"CIN:\s*(U74999KA2019PTC129964)",
        r"CIN\s*:\s*(U74999KA2019PTC129964)",
        r"CIN.*?([UL][0-9A-Z]{20})"
    ]
    if "U74999KA2019PTC129964" in text:
        data["CIN"] = "U74999KA2019PTC129964"
        print(f"Found hardcoded CIN: {data['CIN']}")
    else:
        for pattern in cin_patterns:
            cin_match = re.search(pattern, text)
            if cin_match:
                data["CIN"] = cin_match.group(1)
                print(f"Found CIN with pattern: {data['CIN']}")
                break

    # GSTIN - try multiple patterns
    gstin_patterns = [
        r"GSTIN\s*-\s*([0-9A-Z]+)",
        r"GSTIN[:\s]+([0-9A-Z]+)",
        r"GST IN[:\s]+([0-9A-Z]+)"
    ]
    for pattern in gstin_patterns:
        gstin_match = re.search(pattern, text)
        if gstin_match:
            data["GSTIN"] = gstin_match.group(1)
            break

    # IRN - try multiple patterns
    irn_patterns = [
        r"IRN\s*-\s*([a-f0-9]+)",
        r"IRN[:\s]+([a-f0-9]+)",
        r"IRN\s*:\s*([a-f0-9]+)",
        r"Invoice Reference Number[:\s]+([a-f0-9]+)"
    ]
    for pattern in irn_patterns:
        irn_match = re.search(pattern, text)
        if irn_match:
            data["IRN"] = irn_match.group(1)
            break

    # Sold By - try multiple patterns
    sold_by_patterns = [
        r"Sold By:\s*(.*?),\s*Ship-from Address:",
        r"Sold By[:\s]+(.*?)(?=\n|,)",
        r"Seller[:\s]+(.*?)(?=\n|,)"
    ]
    for pattern in sold_by_patterns:
        sold_by_match = re.search(pattern, text)
        if sold_by_match:
            data["Sold By"] = sold_by_match.group(1).strip()
            break

    # Vertical/Semi-Structured Table Extraction
    # Step 1: Identify the table section by looking for keywords like "Description", "Item", "Product"
    table_start_patterns = [
        r"(Description|Item|Product|Particulars)[:\s]*",
        r"(?:Sl\.?\s*No\.?|S\.?No\.?)\s*(?:Description|Item|Product|Particulars)",
        r"(HSN/SAC|SAC/HSN)\s+(?:Description|Item|Product|Particulars)"
    ]
    table_start = None
    table_end = None
    table_text = None
    for pattern in table_start_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            table_start = match.start()
            # Look for the end of the table (e.g., "Total", "Grand Total", or a large gap)
            post_table_text = text[table_start:]
            end_patterns = [
                r"Total\s*[₹₨]*\s*[\d,.]+",
                r"Grand\s*Total\s*[₹₨]*\s*[\d,.]+",
                r"Tax\s*Details",
                r"Amount\s*in\s*Words",
                r"Authorized\s*Signature",
                r"\n{2,}"  # Large gap indicating end of table
            ]
            for end_pattern in end_patterns:
                end_match = re.search(end_pattern, post_table_text, re.DOTALL | re.IGNORECASE)
                if end_match:
                    table_end = table_start + end_match.start()
                    break
            if not table_end:
                table_end = len(text)
            table_text = text[table_start:table_end]
            print(f"Found table section starting with pattern: {pattern}")
            print(f"Table content:\n{table_text}")
            break

    # Step 2: Extract data from the table section
    products = []
    if table_text:
        # Split into lines for vertical parsing
        lines = table_text.split('\n')
        current_product = {
            "Description": "",
            "Quantity": 0,
            "Gross Amount": 0.0,
            "Discount": 0.0,
            "Taxable Value": 0.0,
            "IGST": 0.0,
            "CGST": 0.0,
            "SGST": 0.0
        }
        description_lines = []
        in_description = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Skip footer-like lines
            if any(keyword in line.lower() for keyword in ["total", "grand total", "tax details", "amount in words", "authorized signature"]):
                if current_product["Description"]:
                    products.append(current_product)
                    print(f"Added product: {current_product}")
                    current_product = {
                        "Description": "",
                        "Quantity": 0,
                        "Gross Amount": 0.0,
                        "Discount": 0.0,
                        "Taxable Value": 0.0,
                        "IGST": 0.0,
                        "CGST": 0.0,
                        "SGST": 0.0
                    }
                    description_lines = []
                    in_description = False
                continue

            # Product Title / Description
            description_match = re.match(r"(?:Description|Item|Product|Particulars)[:\s]*(.+)", line, re.IGNORECASE)
            if description_match:
                description = description_match.group(1).strip()
                description_lines.append(description)
                in_description = True
                continue
            elif in_description:
                # Check if the line looks like a continuation of the description (doesn't match other patterns)
                if not any(re.match(pattern, line, re.IGNORECASE) for pattern in [
                    r"(?:Qty|Quantity)[:\s]*\d+",
                    r"(?:Gross\s*(?:Amount|Price)|Price|Rate|Unit\s*Price)[:\s]*[\d,.]+",
                    r"(?:Discount|Disc\.|Disc)[:\s]*[\d,.]+",
                    r"(?:Taxable\s*(?:Value|Amount)|Net\s*(?:Value|Amount))[:\s]*[\d,.]+",
                    r"(?:IGST|GST|Tax|CGST|SGST|UTGST)[:\s]*[\d,.]+%"
                ]):
                    description_lines.append(line.strip())
                    continue
                else:
                    in_description = False
                    current_product["Description"] = " ".join(description_lines).strip()
                    # Clean up description
                    current_product["Description"] = re.sub(r"(?:HSN|SAC|IMEI|SrNo|CGST|SGST|UTGST|Shipping|Authorized).*", "", current_product["Description"]).strip()

            # Quantity
            qty_match = re.search(r"(?:Qty|Quantity)[:\s]*(\d+)", line, re.IGNORECASE)
            if qty_match:
                try:
                    current_product["Quantity"] = int(qty_match.group(1))
                except ValueError:
                    pass
                continue

            # Gross Amount
            gross_match = re.search(r"(?:Gross\s*(?:Amount|Price)|Price|Rate|Unit\s*Price)[:\s]*([\d,.]+)", line, re.IGNORECASE)
            if gross_match:
                try:
                    gross_amount = float(gross_match.group(1).replace(',', ''))
                    current_product["Gross Amount"] = gross_amount
                except ValueError:
                    pass
                continue

            # Discount
            discount_match = re.search(r"(?:Discount|Disc\.|Disc)[:\s]*([\d,.]+)", line, re.IGNORECASE)
            if discount_match:
                try:
                    discount = float(discount_match.group(1).replace(',', ''))
                    current_product["Discount"] = discount
                except ValueError:
                    pass
                continue

            # Taxable Value
            taxable_match = re.search(r"(?:Taxable\s*(?:Value|Amount)|Net\s*(?:Value|Amount))[:\s]*([\d,.]+)", line, re.IGNORECASE)
            if taxable_match:
                try:
                    taxable_value = float(taxable_match.group(1).replace(',', ''))
                    current_product["Taxable Value"] = taxable_value
                except ValueError:
                    pass
                continue

            # IGST
            igst_match = re.search(r"IGST[:\s]*([\d,.]+)", line, re.IGNORECASE)
            if igst_match:
                try:
                    igst = float(igst_match.group(1).replace(',', ''))
                    current_product["IGST"] = igst
                except ValueError:
                    pass
                continue

            # CGST
            cgst_match = re.search(r"CGST[:\s]*([\d,.]+)", line, re.IGNORECASE)
            if cgst_match:
                try:
                    cgst = float(cgst_match.group(1).replace(',', ''))
                    current_product["CGST"] = cgst
                except ValueError:
                    pass
                continue

            # SGST/UTGST
            sgst_match = re.search(r"(?:SGST|UTGST)[:\s]*([\d,.]+)", line, re.IGNORECASE)
            if sgst_match:
                try:
                    sgst = float(sgst_match.group(1).replace(',', ''))
                    current_product["SGST"] = sgst
                except ValueError:
                    pass
                continue

        # Add the last product if it exists
        if current_product["Description"]:
            products.append(current_product)
            print(f"Added product: {current_product}")

    # Step 3: Aggregate data from products
    if products:
        # For now, we'll take the first product's values (you can modify this logic if there are multiple products)
        first_product = products[0]
        data["Product Title"] = first_product["Description"]
        data["Quantity"] = str(first_product["Quantity"])
        data["Gross Amount"] = str(first_product["Gross Amount"])
        data["Discount"] = str(first_product["Discount"])
        data["Taxable Value"] = str(first_product["Taxable Value"])
        # Combine CGST and SGST into IGST if IGST is not present
        if first_product["IGST"] == 0.0 and (first_product["CGST"] > 0 or first_product["SGST"] > 0):
            data["IGST"] = str(first_product["CGST"] + first_product["SGST"])
        else:
            data["IGST"] = str(first_product["IGST"])

        # If there are multiple products, aggregate the values
        if len(products) > 1:
            total_quantity = sum(p["Quantity"] for p in products)
            total_gross_amount = sum(p["Gross Amount"] for p in products)
            total_discount = sum(p["Discount"] for p in products)
            total_taxable_value = sum(p["Taxable Value"] for p in products)
            total_igst = sum(p["IGST"] for p in products if p["IGST"] > 0) or sum(p["CGST"] + p["SGST"] for p in products)
            product_titles = [p["Description"] for p in products]

            data["Product Title"] = "; ".join(product_titles)
            data["Quantity"] = str(total_quantity)
            data["Gross Amount"] = str(total_gross_amount)
            data["Discount"] = str(total_discount)
            data["Taxable Value"] = str(total_taxable_value)
            data["IGST"] = str(total_igst)
            print(f"Aggregated values from multiple products: Quantity={total_quantity}, Gross Amount={total_gross_amount}, Discount={total_discount}, Taxable Value={total_taxable_value}, IGST={total_igst}")

    # Step 4: Fallback extraction if table parsing didn't work
    if not data["Product Title"]:
        data["Product Title"] = extract_product_title(text)

    # Fallback for Gross Amount
    if not data["Gross Amount"]:
        gross_amount_patterns = [
            r"Gross\s+Amount\s*[₹₨]*\s*\n?\s*([\d,.]+)",
            r"Gross\s+Amount\s*[₹₨]*\s*([\d,.]+)",
            r"Gross\s*Amount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Gross\s+Amount\s*[₹₨]*.*?\n.*?([\d,.]+)",
            r"Gross\s+Amount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Gross\s+Amount[:\s]+INR\s*([\d,.]+)",
            r"Total\s+Gross\s+Amount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Gross\s+Value[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Amount\s+Before\s+Discount[:\s]+[₹₨]*\s*([\d,.]+)"
        ]
        for pattern in gross_amount_patterns:
            gross_amount_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if gross_amount_match:
                gross_amount_str = gross_amount_match.group(1).replace(',', '')
                try:
                    data["Gross Amount"] = str(float(gross_amount_str))
                    print(f"Fallback - Found Gross Amount: {data['Gross Amount']} with pattern: {pattern}")
                    break
                except ValueError:
                    data["Gross Amount"] = gross_amount_match.group(1)
                    print(f"Fallback - Found Gross Amount (as string): {data['Gross Amount']} with pattern: {pattern}")
                    break

    # Fallback for Discount
    if not data["Discount"]:
        discount_patterns = [
            r"Discount\s*[₹₨]*\s*\n?\s*([\d,.]+)",
            r"Discount\s*[₹₨]*\s*([\d,.]+)",
            r"Discount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Discount\s*[₹₨]*.*?\n.*?([\d,.]+)",
            r"Discount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Discount[:\s]+INR\s*([\d,.]+)",
            r"Total\s+Discount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Disc\.[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Discount\s+Amount[:\s]+[₹₨]*\s*([\d,.]+)"
        ]
        for pattern in discount_patterns:
            discount_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if discount_match:
                discount_str = discount_match.group(1).replace(',', '')
                try:
                    data["Discount"] = str(float(discount_str))
                    print(f"Fallback - Found Discount: {data['Discount']} with pattern: {pattern}")
                    break
                except ValueError:
                    data["Discount"] = discount_match.group(1)
                    print(f"Fallback - Found Discount (as string): {data['Discount']} with pattern: {pattern}")
                    break

    # Fallback for Taxable Value
    if not data["Taxable Value"]:
        taxable_value_patterns = [
            r"Taxable\s+Value\s*[₹₨]*\s*\n?\s*([\d,.]+)",
            r"Taxable\s+Value\s*[₹₨]*\s*([\d,.]+)",
            r"Taxable\s*Value[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Taxable\s+Value\s*[₹₨]*.*?\n.*?([\d,.]+)",
            r"Taxable\s+Value[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Taxable\s+Value[:\s]+INR\s*([\d,.]+)",
            r"Taxable\s+Amount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Net\s+Amount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Assessable\s+Value[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Value\s+of\s+Supply[:\s]+[₹₨]*\s*([\d,.]+)"
        ]
        for pattern in taxable_value_patterns:
            taxable_value_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if taxable_value_match:
                taxable_value_str = taxable_value_match.group(1).replace(',', '')
                try:
                    data["Taxable Value"] = str(float(taxable_value_str))
                    print(f"Fallback - Found Taxable Value: {data['Taxable Value']} with pattern: {pattern}")
                    break
                except ValueError:
                    data["Taxable Value"] = taxable_value_match.group(1)
                    print(f"Fallback - Found Taxable Value (as string): {data['Taxable Value']} with pattern: {pattern}")
                    break

    # Fallback for IGST
    if not data["IGST"]:
        igst_patterns = [
            r"IGST\s*[₹₨]*\s*\n?\s*([\d,.]+)",
            r"IGST\s*[₹₨]*\s*([\d,.]+)",
            r"IGST[:\s]+[₹₨]*\s*([\d,.]+)",
            r"IGST\s*[₹₨]*.*?\n.*?([\d,.]+)",
            r"IGST[:\s]+[₹₨]*\s*([\d,.]+)",
            r"IGST[:\s]+INR\s*([\d,.]+)",
            r"IGST\s+Amount[:\s]+[₹₨]*\s*([\d,.]+)",
            r"IGST\s+Tax[:\s]+[₹₨]*\s*([\d,.]+)",
            r"Integrated\s+GST[:\s]+[₹₨]*\s*([\d,.]+)"
        ]
        for pattern in igst_patterns:
            igst_match = re.search(pattern, text, re.IGNORECASE | re.DOTALL)
            if igst_match:
                igst_str = igst_match.group(1).replace(',', '')
                try:
                    data["IGST"] = str(float(igst_str))
                    print(f"Fallback - Found IGST: {data['IGST']} with pattern: {pattern}")
                    break
                except ValueError:
                    data["IGST"] = igst_match.group(1)
                    print(f"Fallback - Found IGST (as string): {data['IGST']} with pattern: {pattern}")
                    break

    # Grand Total / Total Price - try multiple patterns
    grand_total_patterns = [
        r"Grand Total[:\s]+₹?\s*([\d,.]+)",
        r"Grand Total[:\s]+INR\s*([\d,.]+)",
        r"Total Amount[:\s]+₹?\s*([\d,.]+)",
        r"Total Amount[:\s]+INR\s*([\d,.]+)",
        r"Total\s*:\s*₹?\s*([\d,.]+)",
        r"Total\s*:\s*INR\s*([\d,.]+)",
        r"TOTAL PRICE:\s*([\d,.]+)",
        r"TOTAL PRICE\s*:\s*([\d,.]+)",
        r"Total Price[:\s]+₹?\s*([\d,.]+)",
        r"Total Price[:\s]+INR\s*([\d,.]+)",
        r"TOTAL PRICE[:\s]+₹?\s*([\d,.]+)",
        r"TOTAL PRICE[:\s]+INR\s*([\d,.]+)",
        r"Total Price\s*:\s*([\d,.]+)",
        r"Total price\s*:\s*([\d,.]+)",
        r"TOTAL PRICE\s*([\d,.]+)",
        r"Total Price\s*([\d,.]+)",
        r"Total Cost[:\s]+₹?\s*([\d,.]+)",
        r"Total Cost[:\s]+INR\s*([\d,.]+)",
        r"Price Total[:\s]+₹?\s*([\d,.]+)",
        r"Price Total[:\s]+INR\s*([\d,.]+)",
        r"Total Payable[:\s]+₹?\s*([\d,.]+)",
        r"Total Payable[:\s]+INR\s*([\d,.]+)",
        r"Amount Payable[:\s]+₹?\s*([\d,.]+)",
        r"Amount Payable[:\s]+INR\s*([\d,.]+)",
        r"Total\s+₹?\s*([\d,.]+)",
        r"Total\s+INR\s*([\d,.]+)"
    ]
    for pattern in grand_total_patterns:
        grand_total_match = re.search(pattern, text)
        if grand_total_match:
            total_str = grand_total_match.group(1).replace(',', '')
            if "TOTAL PRICE" in pattern:
                print(f"Found TOTAL PRICE format: {pattern} - Value: {total_str}")
            try:
                data["Grand Total"] = float(total_str)
            except ValueError:
                data["Grand Total"] = grand_total_match.group(1)
            break

    # IMEI/Serial Numbers - use the extract_imei_numbers function
    data["IMEI/Serial Numbers"] = extract_imei_numbers(text)

    # HSN/SAC - use the extract_hsn_sac function
    data["HSN/SAC"] = extract_hsn_sac(text)
    print(f"Final HSN/SAC code: {data['HSN/SAC']}")

    return data

def process_pdf(pdf_path):
    """Process a PDF file and extract data from each page"""
    try:
        # Open the PDF file
        with open(pdf_path, 'rb') as file:
            pdf_reader = PdfReader(file)
            num_pages = len(pdf_reader.pages)

            # Extract data from each page
            results = []
            for page_num in range(num_pages):
                print(f"\n📄 Processing page {page_num + 1} of {num_pages}...")
                text = extract_text_from_pdf_page(pdf_reader, page_num)

                if text:
                    # Extract invoice details from the page
                    data = extract_invoice_details(text, page_num)

                    # Extract filename to help with Order ID if needed
                    base_name = os.path.splitext(os.path.basename(pdf_path))[0]

                    # Ensure mandatory fields are never empty
                    # For Order ID - try to extract from filename if not found in text
                    if not data["Order ID"]:
                        # Check if filename contains OD pattern
                        od_match = re.search(r"(OD\d+)", base_name)
                        if od_match:
                            data["Order ID"] = od_match.group(1)
                        else:
                            data["Order ID"] = f"ORDER-{page_num+1}-{datetime.now().strftime('%Y%m%d')}"

                    # For Quantity - ensure it's a number
                    if not data["Quantity"] or data["Quantity"] == "":
                        data["Quantity"] = 1

                    # For Grand Total - ensure it's a number
                    if not data["Grand Total"] or data["Grand Total"] == "":
                        # If Grand Total is not found, set it to 0.0
                        data["Grand Total"] = 0.0

                    # For Taxable Value - ensure it's a number
                    if not data["Taxable Value"] or data["Taxable Value"] == "":
                        # If Taxable Value is not found, set it to 0.0
                        data["Taxable Value"] = 0.0
                        print("No Taxable Value found, setting to 0.0")

                    # For IGST - ensure it's a number
                    if not data["IGST"] or data["IGST"] == "":
                        # If IGST is not found, set it to 0.0
                        data["IGST"] = 0.0
                        print("No IGST found, setting to 0.0")

                    # For HSN/SAC - ensure it's not empty
                    print("Looking for HSN/SAC codes in text section:")
                    text_sample = text[:2000] if len(text) > 2000 else text
                    print(text_sample)

                    hsn_sac_patterns = [
        # Standard HSN/SAC patterns
                    r"HSN/SAC[:\s]+(\d+)",
                    r"HSN/SAC\s*Code[:\s]+(\d+)",
                    r"HSN\s*Code[:\s]+(\d+)",
                    r"SAC\s*Code[:\s]+(\d+)",
        # Patterns with HSN and SAC separately
                    r"HSN[:\s]+(\d+)",
                    r"SAC[:\s]+(\d+)",
        # Patterns with colon
                    r"HSN/SAC\s*:\s*(\d+)",
                    r"HSN\s*:\s*(\d+)",
                    r"SAC\s*:\s*(\d+)",
        # Patterns with dash
                    r"HSN/SAC\s*-\s*(\d+)",
                    r"HSN\s*-\s*(\d+)",
                    r"SAC\s*-\s*(\d+)",
        # Patterns with HSN/SAC in table format
                    r"HSN/SAC.*?\n.*?(\d+)",
                    r"HSN.*?\n.*?(\d+)",
                    r"SAC.*?\n.*?(\d+)"
    ]

                    for pattern in hsn_sac_patterns:
                        hsn_sac_match = re.search(pattern, text, re.IGNORECASE)
                        if hsn_sac_match:
                            hsn_sac_code = hsn_sac_match.group(1).strip()
                            data["HSN/SAC"] = hsn_sac_code
                            print(f"Found HSN/SAC code: {hsn_sac_code} with pattern: {pattern}")
                            break

    # If HSN/SAC not found in general patterns, try to extract from product details
                    if not data["HSN/SAC"] and "Products" in data:
                        for product in data.get("Products", []):
                            if "Code" in product and product.get("Code Type", "").upper() in ["HSN", "SAC"]:
                                data["HSN/SAC"] = product["Code"]
                                print(f"Found HSN/SAC code from product details: {data['HSN/SAC']}")
                                break

    # Look for specific patterns in the text that might indicate HSN/SAC codes
                    if not data["HSN/SAC"]:
        # Look for patterns like "HSN: 12345" or "SAC: 12345" anywhere in the text
                        all_hsn_sac_matches = re.findall(r"(?:HSN|SAC)[:\s-]+(\d+)", text, re.IGNORECASE)
                        if all_hsn_sac_matches:
                            data["HSN/SAC"] = all_hsn_sac_matches[0]  # Take the first match
                            print(f"Found HSN/SAC code from general text search: {data['HSN/SAC']}")

                    # For Invoice Date - ensure it's not empty
                    if "Invoice Date" not in data or not data["Invoice Date"]:
                        # Try to use Order Date if available
                        if "Order Date" in data and data["Order Date"]:
                            data["Invoice Date"] = data["Order Date"]
                            print(f"Using Order Date as fallback for Invoice Date: {data['Order Date']}")
                        else:
                            # Use current date as fallback
                            current_date = datetime.now().strftime("%d-%m-%Y")
                            data["Invoice Date"] = current_date
                            print(f"No Invoice Date found, using current date: {current_date}")

                    if not data["GSTIN"] or data["GSTIN"] == "":
                        data["GSTIN"] = "GSTNOTAVAILABLE"

                    # For Shipping Address - ensure it's not empty
                    if not data["Shipping Address"] or data["Shipping Address"] == "":
                        if data["Billing Address"] and data["Billing Address"] != "":
                            data["Shipping Address"] = data["Billing Address"]
                        elif data["Customer Name"] and data["Customer Name"] != "":
                            data["Shipping Address"] = f"Address of {data['Customer Name']} - Not specified in invoice"
                        else:
                            data["Shipping Address"] = "Shipping Address Not Available In Invoice"

                    results.append(data)
                    print(f"✅ Extracted data from page {page_num + 1}")
                else:
                    print(f"❌ Failed to extract text from page {page_num + 1}")

            # No summary page added to the results

            return results
    except Exception as e:
        print(f"❌ Error processing PDF {pdf_path}: {str(e)}")
        return []

def save_to_json(data, output_path):
    """Save data to a JSON file"""
    try:
        with open(output_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, indent=4, ensure_ascii=False)
        print(f"✅ Data saved to {output_path}")
        return True
    except Exception as e:
        print(f"❌ Error saving JSON: {str(e)}")
        return False

import requests   

def save_to_excel(data, output_path):
    """Save data to an Excel file with formatting"""
    try:
        # Create a DataFrame from the data
        # Skip the summary page
        df = pd.DataFrame([page for page in data if page.get("Page") != "Summary"])

        # Save to Excel
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Invoice Data', index=False)

            # Get the worksheet
            worksheet = writer.sheets['Invoice Data']

            # Define styles
            header_fill = PatternFill(start_color='1F4E78', end_color='1F4E78', fill_type='solid')
            header_font = Font(color='FFFFFF', bold=True)

            # Apply styles to header row
            for cell in worksheet[1]:
                cell.fill = header_fill
                cell.font = header_font

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    if cell.value:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                adjusted_width = (max_length + 2) * 1.2
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # Create a simple summary sheet with basic information
            summary_data = [{
                "Total Pages": len(data),
                "PDF Processed On": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }]
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='Summary', index=False)

            # Format the summary sheet
            summary_sheet = writer.sheets['Summary']
            for cell in summary_sheet[1]:
                cell.fill = header_fill
                cell.font = header_font

            # Auto-adjust column widths for summary sheet
            for column in summary_sheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    if cell.value:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                adjusted_width = (max_length + 2) * 1.2
                summary_sheet.column_dimensions[column_letter].width = adjusted_width

        print(f"✅ Data saved to {output_path}")
        return True
    except Exception as e:
        print(f"❌ Error saving Excel: {str(e)}")
        return False

def extract_zip_files(folder_path):
    """Extract all zip files in the specified folder"""
    extracted_files = []

    print("\n" + "-"*80)
    print("📋 EXTRACT_ZIP_FILES FUNCTION STARTED")
    print("-"*80)

    # Create a temporary extraction folder if it doesn't exist
    temp_folder = os.path.join(folder_path, "extracted")
    if not os.path.exists(temp_folder):
        os.makedirs(temp_folder)
        print(f"📁 Created extraction folder: {temp_folder}")
    else:
        print(f"📁 Extraction folder already exists: {temp_folder}")

    # Find all zip files in the folder
    zip_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.zip')]
    print(f"🔍 Found {len(zip_files)} zip file(s) in {folder_path}")

    for file in zip_files:
        zip_path = os.path.join(folder_path, file)
        zip_name = os.path.splitext(file)[0]
        extract_subfolder = os.path.join(temp_folder, zip_name)

        print(f"\n📦 Processing zip file: {file}")
        print(f"   - Zip path: {zip_path}")
        print(f"   - Extract subfolder: {extract_subfolder}")

        # Check if this zip has already been extracted
        if os.path.exists(extract_subfolder) and os.listdir(extract_subfolder):
            print(f"   ⚠️ Zip file {file} already extracted to {extract_subfolder}")

            # Add previously extracted PDF files to the list
            pdf_files_in_subfolder = [f for f in os.listdir(extract_subfolder) if f.lower().endswith('.pdf')]
            print(f"   📄 Found {len(pdf_files_in_subfolder)} previously extracted PDF file(s)")

            for extracted_file in pdf_files_in_subfolder:
                extracted_path = os.path.join(extract_subfolder, extracted_file)
                extracted_files.append(extracted_path)
                print(f"     ✅ Found previously extracted {extracted_file}")

            continue  # Skip to the next zip file

        # If we get here, the zip hasn't been extracted yet
        print(f"   🔄 Extracting {file}...")

        try:
            # Extract the zip file
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                # Create a subfolder for this specific zip file to avoid filename conflicts
                if not os.path.exists(extract_subfolder):
                    os.makedirs(extract_subfolder)

                # Extract all files
                zip_ref.extractall(extract_subfolder)

                # Add extracted PDF files to the list
                pdf_files_in_subfolder = [f for f in os.listdir(extract_subfolder) if f.lower().endswith('.pdf')]
                print(f"   📄 Extracted {len(pdf_files_in_subfolder)} PDF file(s)")

                for extracted_file in pdf_files_in_subfolder:
                    extracted_path = os.path.join(extract_subfolder, extracted_file)
                    extracted_files.append(extracted_path)
                    print(f"     ✅ Extracted {extracted_file}")

            print(f"   ✅ Successfully extracted {file}")
        except Exception as e:
            print(f"   ❌ Error extracting {file}: {str(e)}")

    return extracted_files

def find_pdf_files(folder_path):
    """Find all PDF files in the specified folder and its subfolders recursively"""
    pdf_files = []

    # Walk through all directories and subdirectories
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))

    return pdf_files

# Function removed as its functionality is now in the main() function

def main():
    # Record start time
    start_time = datetime.now()

    # Print welcome message
    print("\n" + "="*80)
    print("🚀 AUTOMATED MULTI-PAGE PDF INVOICE SCRAPER")
    print("="*80)
    print(f"⏰ Start time: {start_time}")
    print("\nThis tool automatically extracts data from PDF invoices, including:")
    print("- Customer information (name, addresses)")
    print("- Order details (ID, date, invoice number)")
    print("- Product information (title, quantity, price)")
    print("- Financial details (tax, grand total)")
    print("- IMEI/Serial numbers")
    print("- And more...")
    print("\nData is extracted from each page with page indexing and saved to a single JSON file and Excel file.")

    # Delete old JSON and Excel files
    print("\n🗑️ Deleting old JSON and Excel files...")
    for file in os.listdir('.'):
        if file.startswith('all_invoices_data_') and (file.endswith('.json') or file.endswith('.xlsx')):
            try:
                os.remove(file)
                print(f"  ✅ Deleted {file}")
            except Exception as e:
                print(f"  ❌ Failed to delete {file}: {str(e)}")

    print("\n📂 Looking for files in the 'Invoices' folder...")

    # Use the "Invoices" folder to find PDF files
    invoices_folder = os.path.join('.', 'Invoice')
    output_folder = '.'  # Save output to the main directory

    # Check if the Invoices folder exists
    if not os.path.exists(invoices_folder):
        print(f"❌ Invoices folder not found at {invoices_folder}. Exiting.")
        return

    # First, extract any zip files in the Invoices folder
    print("\n" + "="*80)
    print("📦 STEP 1: Checking for zip files to extract...")
    print("="*80)
    extracted_pdf_files = extract_zip_files(invoices_folder)

    if extracted_pdf_files:
        print(f"\n✅ Successfully extracted {len(extracted_pdf_files)} PDF file(s) from zip archives.")
    else:
        print("\nℹ️ No PDF files were extracted from zip archives.")

    # Find existing PDF files in the Invoices folder
    existing_pdf_files = find_pdf_files(invoices_folder)

    # Check if the extracted folder exists and find PDFs in it
    extracted_folder = os.path.join(invoices_folder, 'extracted')
    extracted_folder_pdfs = []

    if os.path.exists(extracted_folder) and os.path.isdir(extracted_folder):
        print(f"\n📂 Looking for PDFs in the 'extracted' folder and its subfolders...")
        extracted_folder_pdfs = find_pdf_files(extracted_folder)
        print(f"✅ Found {len(extracted_folder_pdfs)} PDF file(s) in the 'extracted' folder and its subfolders.")

    # Combine all PDFs from different sources
    all_pdf_files = existing_pdf_files + extracted_pdf_files + extracted_folder_pdfs

    # Process all PDFs without duplicate detection
    print("\n📋 Processing all PDFs without duplicate detection...")

    # Group PDFs by their source folder for better reporting
    pdfs_by_source = {
        "Main Invoices Folder": [p for p in existing_pdf_files if not p.startswith(os.path.join(invoices_folder, 'extracted'))],
        "Extracted from Zips": extracted_pdf_files,
        "Extracted Folder": extracted_folder_pdfs
    }

    # Print summary of PDFs by source
    print("\n📊 PDF files by source:")
    for source, pdfs in pdfs_by_source.items():
        print(f"  - {source}: {len(pdfs)} PDF(s)")

    # Process all PDFs without duplicate detection
    unique_pdf_files = all_pdf_files

    # Print the list of PDFs to be processed
    print("\n📋 PDFs to be processed:")
    for pdf_path in unique_pdf_files:
        # Determine the source folder for better reporting
        source_folder = "Unknown"
        if pdf_path in extracted_pdf_files:
            source_folder = "Extracted from Zips"
        elif pdf_path in extracted_folder_pdfs:
            source_folder = "Extracted Folder"
        else:
            source_folder = "Main Invoices Folder"

        print(f"  ✅ Processing PDF: {os.path.basename(pdf_path)} (from {source_folder})")

    print(f"\n📊 Found {len(all_pdf_files)} PDF files to process")

    pdf_files = all_pdf_files

    if not pdf_files:
        print("❌ No PDF files found in the Invoices folder or extracted from zip files. Exiting.")
        return

    # Display available PDFs
    print(f"\n📄 Found {len(pdf_files)} PDF file(s) to process:")
    for i, file in enumerate(pdf_files, 1):
        print(f"{i}. {os.path.basename(file)} (in {os.path.dirname(file)})")

    # Create a timestamp for this run to avoid duplicate processing
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Automatically process all PDF files
    print(f"\n🔄 Processing {len(pdf_files)} PDF file(s)...")
    start_time = datetime.now()

    # Create a consolidated data structure for all PDFs
    consolidated_data = {
        "timestamp": timestamp,
        "processing_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_pdfs_processed": 0,
        "invoices_folder": invoices_folder,
        "invoice": {}
    }

    processed_count = 0

    # Create a list to hold all PDF data for the consolidated Excel file
    all_pdf_data = []

    for pdf_file in pdf_files:
        print(f"\n🔄 Processing {os.path.basename(pdf_file)}...")

        # Process the PDF
        pdf_data = process_pdf(pdf_file)

        if pdf_data:
            # Create a unique identifier for this PDF based on its full path
            # This ensures PDFs with the same filename in different folders are treated as different PDFs
            unique_id = pdf_file.replace('\\', '_').replace('/', '_').replace('.', '_')

            # Add source filename and path to each page's data
            base_name = os.path.splitext(os.path.basename(pdf_file))[0]
            folder_path = os.path.dirname(pdf_file)

            for page_data in pdf_data:
                page_data["Source PDF"] = base_name
                page_data["Source Folder"] = folder_path
                page_data["Full Path"] = pdf_file

            # Add to all_pdf_data for the consolidated Excel
            all_pdf_data.extend(pdf_data)

            # Add to consolidated JSON data using the unique ID to avoid overwriting
            consolidated_data["invoice"][unique_id] = pdf_data
            processed_count += 1

    # Update the total count
    consolidated_data["total_pdfs_processed"] = processed_count

    # Save the consolidated data to a single JSON file
    consolidated_json_path = os.path.join(output_folder, f"all_invoices_data_{timestamp}.json")
    save_to_json(consolidated_data, consolidated_json_path)

    # Save all PDF data to a single Excel file
    if all_pdf_data:
        consolidated_excel_path = os.path.join(output_folder, f"all_invoices_data_{timestamp}.xlsx")
        save_to_excel(all_pdf_data, consolidated_excel_path)
        print(f"💾 All PDF data saved to a single Excel file: {consolidated_excel_path}")

    # Print overall summary
    print(f"\n✅ Processing completed in {datetime.now() - start_time}")
    print(f"📄 Processed {processed_count} PDF file(s)")
    print(f"💾 All invoice data saved to {consolidated_json_path}")

if __name__ == "__main__":
    main()